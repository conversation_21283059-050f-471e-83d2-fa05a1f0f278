<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Dashboard - TaskFlow</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Styles -->
    <style>
        :root {
            --primary-blue: #2563eb;
            --dark-blue: #1e40af;
            --light-blue: #3b82f6;
            --accent-blue: #60a5fa;
            --bg-white: #ffffff;
            --bg-gray: #f8fafc;
            --bg-light: #f1f5f9;
            --text-dark: #1e293b;
            --text-gray: #64748b;
            --text-light: #94a3b8;
            --border-light: #e2e8f0;
            --border-blue: #bfdbfe;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-blue: 0 4px 14px 0 rgba(37, 99, 235, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--bg-white);
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-light);
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 800;
            color: white;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-blue);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 1rem;
            background: var(--bg-gray);
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-menu:hover {
            background: var(--bg-light);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--accent-blue), var(--primary-blue));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 0.875rem;
        }haidi

        /* Main Layout */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            display: grid;
            gap: 2rem;
        }

        /* Welcome Section */
        .welcome-section {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 16px;
            padding: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(50%, -50%);
        }

        .welcome-content {
            position: relative;
            z-index: 2;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.25rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        /* Projects Section */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        .btn-secondary {
            background: var(--bg-white);
            color: var(--primary-blue);
            border: 2px solid var(--primary-blue);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-blue);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: var(--bg-white);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .project-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-blue);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .project-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--accent-blue), var(--primary-blue));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .project-menu {
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .project-menu:hover {
            background: var(--bg-light);
            color: var(--text-gray);
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .project-description {
            color: var(--text-gray);
            font-size: 0.875rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .project-progress {
            margin-bottom: 1rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-gray);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .progress-percentage {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-blue);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--bg-light);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .project-team {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .team-avatars {
            display: flex;
            gap: -0.5rem;
        }

        .team-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--accent-blue), var(--primary-blue));
            border-radius: 50%;
            border: 2px solid var(--bg-white);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            margin-left: -0.5rem;
        }

        .team-avatar:first-child {
            margin-left: 0;
        }

        .project-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-blue);
        }

        .status-text {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-gray);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .action-card {
            background: var(--bg-white);
            border: 1px solid var(--border-light);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-blue);
        }

        .action-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin: 0 auto 1rem;
        }

        .action-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .action-description {
            color: var(--text-gray);
            font-size: 0.875rem;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--bg-white);
            border-radius: 16px;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--bg-light);
            color: var(--text-gray);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border-light);
            border-radius: 8px;
            background: var(--bg-white);
            font-size: 1rem;
            color: var(--text-dark);
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: 2px dashed var(--border-light);
            border-radius: 8px;
            background: var(--bg-gray);
            color: var(--text-gray);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-label:hover {
            border-color: var(--primary-blue);
            background: rgba(37, 99, 235, 0.05);
            color: var(--primary-blue);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-light);
        }

        .btn-cancel {
            background: var(--bg-gray);
            color: var(--text-gray);
            border: 1px solid var(--border-light);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel:hover {
            background: var(--bg-light);
            color: var(--text-dark);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .header-actions {
                justify-content: space-between;
            }

            .main-container {
                padding: 1rem;
            }

            .welcome-section {
                padding: 1.5rem;
            }

            .welcome-title {
                font-size: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .projects-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .team-avatars {
                display: none;
            }

            .project-header {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">TF</div>
                <div class="logo-text">TaskFlow</div>
            </div>

            <div class="header-actions">
                <button class="btn-primary" onclick="openCreateProjectModal()">
                    ➕ Nouveau Teams
                </button>

                <div class="user-menu">
                    <div class="user-avatar">{{ strtoupper(substr($user->name, 0, 2)) }}</div>
                    <span style="font-weight: 500; color: var(--text-dark);">{{ $user->name }}</span>
                    @if($user->role === 'leader')
                        <span style="color: var(--primary-blue); font-size: 0.75rem; font-weight: 600;">👑 LEADER</span>
                    @endif
                    <span style="color: var(--text-light);">▼</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">

        <!-- Welcome Section -->
        <section class="welcome-section">
            <div class="welcome-content">
                <h1 class="welcome-title">Bienvenue sur TaskFlow ! 👋</h1>
                <p class="welcome-subtitle">
                    Gérez vos projets efficacement et collaborez avec votre équipe en temps réel.
                </p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">Projets Actifs</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">48</div>
                        <div class="stat-label">Tâches Complétées</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">6</div>
                        <div class="stat-label">Membres Équipe</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">94%</div>
                        <div class="stat-label">Taux Réussite</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section>
            <div class="section-header">
                <h2 class="section-title">Actions Rapides</h2>
            </div>

            <div class="quick-actions">
                <div class="action-card" onclick="openCreateProjectModal()">
                    <div class="action-icon">🚀</div>
                    <h3 class="action-title">Créer un Projet</h3>
                    <p class="action-description">Démarrez un nouveau projet et invitez votre équipe</p>
                </div>

                <div class="action-card" onclick="openInviteMemberModal()">
                    <div class="action-icon">👥</div>
                    <h3 class="action-title">Ajouter des Membres</h3>
                    <p class="action-description">Invitez de nouveaux collaborateurs à rejoindre vos projets</p>
                </div>

                <div class="action-card" onclick="viewReports()">
                    <div class="action-icon">📊</div>
                    <h3 class="action-title">Voir les Rapports</h3>
                    <p class="action-description">Analysez les performances et le progrès de vos projets</p>
                </div>
            </div>
        </section>

        @if($user->role === 'leader' && $teams->count() > 0)
        <!-- Teams Section -->
        <section>
            <div class="section-header">
                <h2 class="section-title">👑 Mes Équipes (Leader)</h2>
                <button class="btn-secondary" onclick="openCreateTeamModal()">
                    ➕ Nouvelle Équipe
                </button>
            </div>

            <div class="projects-grid">
                @foreach($teams as $team)
                <div class="project-card">
                    <div class="project-header">
                        <div class="project-icon">
                            @if($team->image)
                                <img src="{{ asset('storage/' . $team->image) }}" alt="{{ $team->name }}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 12px;">
                            @else
                                👥
                            @endif
                        </div>
                        <div class="project-menu">⋮</div>
                    </div>

                    <h3 class="project-title">{{ $team->name }}</h3>
                    <p class="project-description">
                        {{ Str::limit($team->description, 100) }}
                    </p>

                    <div class="project-progress">
                        <div class="progress-header">
                            <span class="progress-label">Membres</span>
                            <span class="progress-percentage">{{ $team->members->count() }} membre(s)</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ min(($team->members->count() / 10) * 100, 100) }}%"></div>
                        </div>
                    </div>

                    <div class="project-team">
                        <div class="team-avatars">
                            @foreach($team->members->take(4) as $member)
                                <div class="team-avatar">{{ strtoupper(substr($member->name, 0, 2)) }}</div>
                            @endforeach
                            @if($team->members->count() > 4)
                                <div class="team-avatar">+{{ $team->members->count() - 4 }}</div>
                            @endif
                        </div>
                        <div class="project-status">
                            <div class="status-dot" style="background: #10b981;"></div>
                            <span class="status-text">Leader</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </section>
        @endif

        <!-- Projects Section -->
        <section>
            <div class="section-header">
                <h2 class="section-title">Mes Projets</h2>
                <button class="btn-secondary" onclick="openCreateProjectModal()">
                    ➕ Nouveau Projet
                </button>
            </div>

            <div class="projects-grid">
                <!-- Project Card 1 -->
                <div class="project-card">
                    <div class="project-header">
                        <div class="project-icon">🎯</div>
                        <div class="project-menu">⋮</div>
                    </div>

                    <h3 class="project-title">Site Web E-commerce</h3>
                    <p class="project-description">
                        Développement d'une plateforme e-commerce moderne avec React et Laravel
                    </p>

                    <div class="project-progress">
                        <div class="progress-header">
                            <span class="progress-label">Progression</span>
                            <span class="progress-percentage">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="project-team">
                        <div class="team-avatars">
                            <div class="team-avatar">JD</div>
                            <div class="team-avatar">AM</div>
                            <div class="team-avatar">SL</div>
                            <div class="team-avatar">+2</div>
                        </div>
                        <div class="project-status">
                            <div class="status-dot"></div>
                            <span class="status-text">En cours</span>
                        </div>
                    </div>
                </div>

                <!-- Project Card 2 -->
                <div class="project-card">
                    <div class="project-header">
                        <div class="project-icon">📱</div>
                        <div class="project-menu">⋮</div>
                    </div>

                    <h3 class="project-title">App Mobile TaskFlow</h3>
                    <p class="project-description">
                        Application mobile native pour la gestion de tâches en déplacement
                    </p>

                    <div class="project-progress">
                        <div class="progress-header">
                            <span class="progress-label">Progression</span>
                            <span class="progress-percentage">45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                    </div>

                    <div class="project-team">
                        <div class="team-avatars">
                            <div class="team-avatar">JD</div>
                            <div class="team-avatar">MK</div>
                            <div class="team-avatar">RL</div>
                        </div>
                        <div class="project-status">
                            <div class="status-dot"></div>
                            <span class="status-text">En cours</span>
                        </div>
                    </div>
                </div>

                <!-- Project Card 3 -->
                <div class="project-card">
                    <div class="project-header">
                        <div class="project-icon">🔧</div>
                        <div class="project-menu">⋮</div>
                    </div>

                    <h3 class="project-title">API Gateway</h3>
                    <p class="project-description">
                        Mise en place d'une API Gateway pour microservices avec authentification
                    </p>

                    <div class="project-progress">
                        <div class="progress-header">
                            <span class="progress-label">Progression</span>
                            <span class="progress-percentage">90%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%"></div>
                        </div>
                    </div>

                    <div class="project-team">
                        <div class="team-avatars">
                            <div class="team-avatar">JD</div>
                            <div class="team-avatar">TH</div>
                        </div>
                        <div class="project-status">
                            <div class="status-dot" style="background: #10b981;"></div>
                            <span class="status-text">Presque fini</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Modal Créer une Équipe -->
    <div id="createTeamModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Créer une Nouvelle Équipe</h3>
                <button class="modal-close" onclick="closeCreateTeamModal()">&times;</button>
            </div>

            <form action="{{ route('teams.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="form-group">
                    <label for="team_name" class="form-label">Nom de l'équipe *</label>
                    <input
                        type="text"
                        id="team_name"
                        name="name"
                        class="form-input"
                        placeholder="Ex: Équipe Développement Web"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="team_description" class="form-label">Description *</label>
                    <textarea
                        id="team_description"
                        name="description"
                        class="form-textarea"
                        placeholder="Décrivez les objectifs et la mission de votre équipe..."
                        required
                    ></textarea>
                </div>

                <div class="form-group">
                    <label for="team_image" class="form-label">Image de l'équipe (optionnel)</label>
                    <div class="file-input-wrapper">
                        <input
                            type="file"
                            id="team_image"
                            name="image"
                            class="file-input"
                            accept="image/*"
                        >
                        <label for="team_image" class="file-input-label">
                            📷 Choisir une image
                        </label>
                    </div>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn-cancel" onclick="closeCreateTeamModal()">
                        Annuler
                    </button>
                    <button type="submit" class="btn-primary">
                        🚀 Créer l'Équipe
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function openCreateProjectModal() {
            openCreateTeamModal();
        }

        function openCreateTeamModal() {
            document.getElementById('createTeamModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeCreateTeamModal() {
            document.getElementById('createTeamModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        function openInviteMemberModal() {
            alert('Fonctionnalité "Ajouter des Membres" - À implémenter avec un modal');
        }

        function viewReports() {
            alert('Fonctionnalité "Voir les Rapports" - À implémenter');
        }

        // Animation des cartes au chargement
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.project-card, .action-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>

</body>
</html>