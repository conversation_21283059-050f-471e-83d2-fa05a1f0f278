<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Inscription - TaskFlow</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                position: relative;
                overflow-x: hidden;
            }

            /* Animated background elements */
            body::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
                background-size: 50px 50px;
                animation: float 20s ease-in-out infinite;
                z-index: 1;
            }

            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(180deg); }
            }

            .container {
                width: 100%;
                max-width: 420px;
                position: relative;
                z-index: 2;
                margin: auto;
            }

            .logo-section {
                text-align: center;
                margin-bottom: 40px;
                animation: slideDown 0.8s ease-out;
            }

            .logo {
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
                border-radius: 16px;
                margin: 0 auto 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: bold;
                color: white;
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            }

            .header h1 {
                font-size: 32px;
                font-weight: 700;
                color: white;
                margin-bottom: 8px;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .header p {
                color: rgba(255,255,255,0.8);
                font-size: 16px;
                font-weight: 400;
            }

            .form-container {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                padding: 40px;
                box-shadow:
                    0 20px 40px rgba(0,0,0,0.1),
                    0 1px 3px rgba(0,0,0,0.05),
                    inset 0 1px 0 rgba(255,255,255,0.6);
                animation: slideUp 0.8s ease-out 0.2s both;
            }

            @keyframes slideDown {
                from { opacity: 0; transform: translateY(-30px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes slideUp {
                from { opacity: 0; transform: translateY(30px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .alert {
                padding: 16px 20px;
                border-radius: 12px;
                margin-bottom: 24px;
                font-size: 14px;
                font-weight: 500;
                animation: fadeIn 0.5s ease-out;
            }

            .alert-success {
                background: linear-gradient(135deg, #d4edda, #c3e6cb);
                border: 1px solid #b8dacc;
                color: #155724;
            }

            .alert-error {
                background: linear-gradient(135deg, #f8d7da, #f1b0b7);
                border: 1px solid #f1b0b7;
                color: #721c24;
            }

            .form-group {
                margin-bottom: 24px;
                position: relative;
            }

            .form-label {
                display: block;
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                margin-bottom: 8px;
                transition: color 0.3s ease;
            }

            .form-input {
                width: 100%;
                padding: 16px 20px;
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                background: #ffffff;
                font-size: 16px;
                font-weight: 400;
                color: #111827;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .form-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow:
                    0 0 0 4px rgba(102, 126, 234, 0.1),
                    0 4px 12px rgba(0,0,0,0.15);
                transform: translateY(-2px);
            }

            .form-input::placeholder {
                color: #9ca3af;
                font-weight: 400;
            }

            .btn {
                width: 100%;
                padding: 16px 24px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow:
                    0 4px 12px rgba(102, 126, 234, 0.4),
                    0 2px 4px rgba(0,0,0,0.1);
                position: relative;
                overflow: hidden;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow:
                    0 8px 24px rgba(102, 126, 234, 0.4),
                    0 4px 8px rgba(0,0,0,0.15);
            }

            .btn:hover::before {
                left: 100%;
            }

            .btn:active {
                transform: translateY(0);
            }

            .text-center {
                text-align: center;
            }

            .link {
                color: #667eea;
                text-decoration: none;
                font-weight: 600;
                transition: all 0.3s ease;
                position: relative;
            }

            .link::after {
                content: '';
                position: absolute;
                width: 0;
                height: 2px;
                bottom: -2px;
                left: 50%;
                background: linear-gradient(135deg, #667eea, #764ba2);
                transition: all 0.3s ease;
                transform: translateX(-50%);
            }

            .link:hover::after {
                width: 100%;
            }

            .footer {
                text-align: center;
                margin-top: 24px;
                font-size: 12px;
                color: rgba(255,255,255,0.7);
                animation: fadeIn 0.8s ease-out 0.4s both;
            }

            .footer-links {
                display: flex;
                justify-content: center;
                gap: 20px;
                margin-bottom: 12px;
                flex-wrap: wrap;
            }

            .footer-links a {
                color: rgba(255,255,255,0.8);
                text-decoration: none;
                font-weight: 500;
                font-size: 13px;
                transition: all 0.3s ease;
                padding: 4px 8px;
                border-radius: 6px;
            }

            .footer-links a:hover {
                color: #ffffff;
                background: rgba(255,255,255,0.1);
                transform: translateY(-1px);
            }

            .footer-legal {
                font-size: 11px;
                color: rgba(255,255,255,0.6);
                margin-top: 8px;
            }

            .footer-legal a {
                color: rgba(255,255,255,0.7);
                text-decoration: none;
                transition: color 0.3s ease;
            }

            .footer-legal a:hover {
                color: #ffffff;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            /* Responsive */
            @media (max-width: 480px) {
                body {
                    padding: 10px;
                    align-items: flex-start;
                    padding-top: 20px;
                }

                .container {
                    max-width: 100%;
                }

                .form-container {
                    padding: 24px;
                    border-radius: 20px;
                }

                .header h1 {
                    font-size: 28px;
                }

                .form-input {
                    padding: 14px 16px;
                    font-size: 16px;
                }

                .btn {
                    padding: 14px 20px;
                }

                .logo-section {
                    margin-bottom: 30px;
                }

                .footer-links {
                    gap: 12px;
                    margin-bottom: 8px;
                }

                .footer-links a {
                    font-size: 12px;
                    padding: 3px 6px;
                }

                .footer {
                    margin-top: 16px;
                }
            }
        </style>
    @endif
</head>
<body>

    <div class="container">
        <!-- Logo & Header -->
        <div class="logo-section">
            <div class="logo">TF</div>
            <div class="header">
                <h1>Créer un compte</h1>
                <p>Rejoignez TaskFlow pour gérer vos tâches efficacement</p>
            </div>
        </div>

        <!-- Formulaire d'inscription -->
        <div class="form-container">

            @if (session('success'))
                <div class="alert alert-success">
                    <p>{{ session('success') }}</p>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-error">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('inscrire') }}">
                @csrf


                <!-- Nom complet -->
                <div class="form-group">
                    <label for="name" class="form-label">
                        Nom complet
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name') }}"
                        required
                        autofocus
                        class="form-input"
                        placeholder="Entrez votre nom complet"
                    >
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">
                        Adresse email
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value="{{ old('email') }}"
                        required
                        class="form-input"
                        placeholder="<EMAIL>"
                    >
                </div>

                <!-- Mot de passe -->
                <div class="form-group">
                    <label for="password" class="form-label">
                        Mot de passe
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        class="form-input"
                        placeholder="Créez un mot de passe sécurisé"
                    >
                </div>

                <!-- Confirmation mot de passe -->
                <div class="form-group">
                    <label for="password_confirmation" class="form-label">
                        Confirmer le mot de passe
                    </label>
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        required
                        class="form-input"
                        placeholder="Confirmez votre mot de passe"
                    >
                </div>

                <!-- Bouton d'inscription -->
                <button type="submit" class="btn">
                    Créer mon compte
                </button>

                {{-- <!-- Lien vers connexion -->
                <div class="text-center" style="padding-top: 16px;">
                    <p style="font-size: 14px; color: #706f6c;">
                        Vous avez déjà un compte ?
                        <a href="{{ route('login') }}" class="link">
                            Se connecter
                        </a>
                    </p>
                </div> --}}
            </form>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="#" onclick="return false;">Aide & Support</a>
                <a href="#" onclick="return false;">Contact</a>
                <a href="#" onclick="return false;">À propos</a>
                <a href="#" onclick="return false;">Blog</a>
                <a href="#" onclick="return false;">Carrières</a>
                <a href="#" onclick="return false;">API</a>
            </div>
            <div class="footer-legal">
                En créant un compte, vous acceptez nos
                <a href="#" onclick="return false;">Conditions d'utilisation</a>
                et notre
                <a href="#" onclick="return false;">Politique de confidentialité</a>
                <br>
                © 2024 TaskFlow. Tous droits réservés.
            </div>
        </div>
    </div>

</body>
</html>