<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Rejoignez TaskFlow - Créez votre compte</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
        <style>
            :root {
                --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
                --surface-glass: rgba(255, 255, 255, 0.08);
                --surface-glass-hover: rgba(255, 255, 255, 0.12);
                --text-primary: #ffffff;
                --text-secondary: rgba(255, 255, 255, 0.8);
                --text-muted: rgba(255, 255, 255, 0.6);
                --accent-blue: #3b82f6;
                --accent-purple: #8b5cf6;
                --success-green: #10b981;
                --error-red: #ef4444;
                --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.12);
                --shadow-elevated: 0 20px 40px rgba(0, 0, 0, 0.15);
                --border-glass: 1px solid rgba(255, 255, 255, 0.1);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background: var(--primary-gradient);
                min-height: 100vh;
                position: relative;
                overflow-x: hidden;
                display: flex;
                flex-direction: column;
            }

            /* Animated background effects */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
                z-index: 1;
                pointer-events: none;
            }

            body::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
                animation: drift 20s ease-in-out infinite;
                z-index: 1;
                pointer-events: none;
            }

            @keyframes drift {
                0%, 100% { transform: translateX(0px) translateY(0px); }
                33% { transform: translateX(30px) translateY(-30px); }
                66% { transform: translateX(-20px) translateY(20px); }
            }

            /* Layout */
            .app-container {
                flex: 1;
                display: grid;
                grid-template-columns: 1fr 1fr;
                min-height: 100vh;
                position: relative;
                z-index: 2;
            }

            /* Left Panel - Branding */
            .brand-panel {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 4rem 2rem;
                position: relative;
            }

            .brand-content {
                max-width: 480px;
                text-align: center;
                animation: slideInLeft 0.8s ease-out;
            }

            .brand-logo {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.9) 100%);
                border-radius: 20px;
                margin: 0 auto 2rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 32px;
                font-weight: 800;
                color: var(--accent-purple);
                box-shadow: var(--shadow-elevated);
                backdrop-filter: blur(20px);
                border: var(--border-glass);
            }

            .brand-title {
                font-size: 3.5rem;
                font-weight: 800;
                color: var(--text-primary);
                margin-bottom: 1rem;
                line-height: 1.1;
                background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.8) 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            .brand-subtitle {
                font-size: 1.25rem;
                color: var(--text-secondary);
                margin-bottom: 2rem;
                line-height: 1.6;
                font-weight: 400;
            }

            .brand-features {
                display: flex;
                flex-direction: column;
                gap: 1rem;
                text-align: left;
            }

            .feature-item {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                background: var(--surface-glass);
                border-radius: 12px;
                backdrop-filter: blur(20px);
                border: var(--border-glass);
                transition: all 0.3s ease;
            }

            .feature-item:hover {
                background: var(--surface-glass-hover);
                transform: translateY(-2px);
            }

            .feature-icon {
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                color: white;
                flex-shrink: 0;
            }

            .feature-text {
                color: var(--text-secondary);
                font-weight: 500;
            }

            /* Right Panel - Form */
            .form-panel {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding: 2rem;
                background: rgba(255, 255, 255, 0.02);
                backdrop-filter: blur(20px);
                border-left: var(--border-glass);
            }

            .form-container {
                width: 100%;
                max-width: 420px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(40px);
                border: var(--border-glass);
                border-radius: 24px;
                padding: 2.5rem;
                box-shadow: var(--shadow-elevated);
                animation: slideInRight 0.8s ease-out 0.2s both;
            }

            .form-header {
                text-align: center;
                margin-bottom: 2rem;
            }

            .form-title {
                font-size: 2rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .form-subtitle {
                color: var(--text-secondary);
                font-size: 1rem;
                font-weight: 400;
            }

            /* Animations */
            @keyframes slideInLeft {
                from { opacity: 0; transform: translateX(-50px); }
                to { opacity: 1; transform: translateX(0); }
            }

            @keyframes slideInRight {
                from { opacity: 0; transform: translateX(50px); }
                to { opacity: 1; transform: translateX(0); }
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }

            /* Form Elements */
            .alert {
                padding: 1rem 1.25rem;
                border-radius: 12px;
                margin-bottom: 1.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                animation: fadeIn 0.5s ease-out;
                backdrop-filter: blur(20px);
                border: var(--border-glass);
            }

            .alert-success {
                background: rgba(16, 185, 129, 0.1);
                color: var(--success-green);
                border-color: rgba(16, 185, 129, 0.2);
            }

            .alert-error {
                background: rgba(239, 68, 68, 0.1);
                color: var(--error-red);
                border-color: rgba(239, 68, 68, 0.2);
            }

            .form-group {
                margin-bottom: 1.5rem;
                position: relative;
            }

            .form-label {
                display: block;
                font-size: 0.875rem;
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
                transition: all 0.3s ease;
            }

            .form-input {
                width: 100%;
                padding: 1rem 1.25rem;
                border: 2px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                background: rgba(255, 255, 255, 0.05);
                backdrop-filter: blur(20px);
                font-size: 1rem;
                font-weight: 400;
                color: var(--text-primary);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-glass);
            }

            .form-input:focus {
                outline: none;
                border-color: var(--accent-purple);
                background: rgba(255, 255, 255, 0.08);
                box-shadow:
                    0 0 0 4px rgba(139, 92, 246, 0.1),
                    var(--shadow-elevated);
                transform: translateY(-2px);
            }

            .form-input::placeholder {
                color: var(--text-muted);
                font-weight: 400;
            }

            .btn {
                width: 100%;
                padding: 1rem 1.5rem;
                background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-purple) 100%);
                color: var(--text-primary);
                border: none;
                border-radius: 12px;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-elevated);
                position: relative;
                overflow: hidden;
                margin-top: 0.5rem;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.6s ease;
            }

            .btn:hover {
                transform: translateY(-3px);
                box-shadow:
                    0 12px 32px rgba(59, 130, 246, 0.3),
                    0 4px 12px rgba(0,0,0,0.1);
                animation: pulse 2s infinite;
            }

            .btn:hover::before {
                left: 100%;
            }

            .btn:active {
                transform: translateY(-1px);
            }

            /* Footer */
            .footer {
                background: rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(20px);
                border-top: var(--border-glass);
                padding: 1rem 2rem;
                text-align: center;
                position: relative;
                z-index: 2;
            }

            .footer-content {
                max-width: 1200px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .footer-links {
                display: flex;
                gap: 2rem;
                flex-wrap: wrap;
            }

            .footer-links a {
                color: var(--text-secondary);
                text-decoration: none;
                font-weight: 500;
                font-size: 0.875rem;
                transition: all 0.3s ease;
                padding: 0.5rem;
                border-radius: 8px;
            }

            .footer-links a:hover {
                color: var(--text-primary);
                background: var(--surface-glass);
                transform: translateY(-1px);
            }

            .footer-legal {
                font-size: 0.75rem;
                color: var(--text-muted);
            }

            /* Responsive Design */
            @media (max-width: 1024px) {
                .app-container {
                    grid-template-columns: 1fr;
                }

                .brand-panel {
                    padding: 2rem;
                    min-height: 40vh;
                }

                .brand-title {
                    font-size: 2.5rem;
                }

                .brand-features {
                    display: none;
                }

                .form-panel {
                    border-left: none;
                    border-top: var(--border-glass);
                }
            }

            @media (max-width: 768px) {
                .brand-panel {
                    padding: 1.5rem;
                    min-height: 30vh;
                }

                .brand-title {
                    font-size: 2rem;
                }

                .brand-subtitle {
                    font-size: 1rem;
                }

                .form-container {
                    padding: 2rem;
                }

                .form-title {
                    font-size: 1.5rem;
                }
            }

            @media (max-width: 480px) {
                .brand-panel {
                    padding: 1rem;
                    min-height: 25vh;
                }

                .brand-logo {
                    width: 60px;
                    height: 60px;
                    font-size: 24px;
                }

                .brand-title {
                    font-size: 1.75rem;
                }

                .form-panel {
                    padding: 1rem;
                }

                .form-container {
                    padding: 1.5rem;
                    border-radius: 20px;
                }

                .footer {
                    padding: 1rem;
                }

                .footer-content {
                    flex-direction: column;
                    text-align: center;
                }

                .footer-links {
                    gap: 1rem;
                    justify-content: center;
                }
            }
        </style>
    @endif
</head>
<body>

    <!-- Application Container -->
    <div class="app-container">

        <!-- Left Panel - Branding -->
        <div class="brand-panel">
            <div class="brand-content">
                <div class="brand-logo">TF</div>
                <h1 class="brand-title">TaskFlow</h1>
                <p class="brand-subtitle">
                    La plateforme de gestion de tâches nouvelle génération.
                    Organisez, collaborez et réussissez ensemble.
                </p>

                <div class="brand-features">
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-text">Interface ultra-rapide et intuitive</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-text">Gestion de projets avancée</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">👥</div>
                        <div class="feature-text">Collaboration en temps réel</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <div class="feature-text">Analytics et rapports détaillés</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Form -->
        <div class="form-panel">
            <div class="form-container">
                <div class="form-header">
                    <h2 class="form-title">Créer votre compte</h2>
                    <p class="form-subtitle">Rejoignez des milliers d'équipes qui font confiance à TaskFlow</p>
                </div>

                @if (session('success'))
                    <div class="alert alert-success">
                        <p>{{ session('success') }}</p>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-error">
                        <ul style="list-style: none; padding: 0;">
                            @foreach ($errors->all() as $error)
                                <li>• {{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form method="POST" action="{{ route('inscrire') }}">
                    @csrf

                    <!-- Nom complet -->
                    <div class="form-group">
                        <label for="name" class="form-label">Nom complet</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            value="{{ old('name') }}"
                            required
                            autofocus
                            class="form-input"
                            placeholder="Entrez votre nom complet"
                        >
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">Adresse email</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            value="{{ old('email') }}"
                            required
                            class="form-input"
                            placeholder="<EMAIL>"
                        >
                    </div>

                    <!-- Mot de passe -->
                    <div class="form-group">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            class="form-input"
                            placeholder="Minimum 6 caractères"
                        >
                    </div>

                    <!-- Confirmation mot de passe -->
                    <div class="form-group">
                        <label for="password_confirmation" class="form-label">Confirmer le mot de passe</label>
                        <input
                            type="password"
                            id="password_confirmation"
                            name="password_confirmation"
                            required
                            class="form-input"
                            placeholder="Confirmez votre mot de passe"
                        >
                    </div>

                    <!-- Bouton d'inscription -->
                    <button type="submit" class="btn">
                        🚀 Créer mon compte TaskFlow
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#" onclick="return false;">Support</a>
                <a href="#" onclick="return false;">Contact</a>
                <a href="#" onclick="return false;">À propos</a>
                <a href="#" onclick="return false;">Carrières</a>
                <a href="#" onclick="return false;">API</a>
            </div>
            <div class="footer-legal">
                © 2024 TaskFlow. Tous droits réservés.
            </div>
        </div>
    </div>

</body>
</html>