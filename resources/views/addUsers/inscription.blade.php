<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Inscription - TaskFlow</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Instrument Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #FDFDFC;
                color: #1b1b18;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 24px;
            }

            .container {
                width: 100%;
                max-width: 400px;
            }

            .header {
                text-align: center;
                margin-bottom: 32px;
            }

            .header h1 {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 8px;
            }

            .header p {
                color: #706f6c;
                font-size: 14px;
            }

            .form-container {
                background: white;
                border: 1px solid rgba(26,26,0,0.16);
                border-radius: 8px;
                padding: 24px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .alert {
                padding: 12px;
                border-radius: 6px;
                margin-bottom: 16px;
                font-size: 14px;
            }

            .alert-success {
                background-color: #f0fdf4;
                border: 1px solid #bbf7d0;
                color: #166534;
            }

            .alert-error {
                background-color: #fef2f2;
                border: 1px solid #fecaca;
                color: #dc2626;
            }

            .form-group {
                margin-bottom: 16px;
            }

            .form-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 8px;
            }

            .form-input {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #e3e3e0;
                border-radius: 6px;
                background: #FDFDFC;
                font-size: 14px;
                transition: border-color 0.2s, box-shadow 0.2s;
            }

            .form-input:focus {
                outline: none;
                border-color: #f53003;
                box-shadow: 0 0 0 3px rgba(245, 48, 3, 0.1);
            }

            .btn {
                width: 100%;
                padding: 10px 20px;
                background: #1b1b18;
                color: white;
                border: 1px solid #1b1b18;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .btn:hover {
                background: #000;
            }

            .text-center {
                text-align: center;
            }

            .link {
                color: #f53003;
                text-decoration: none;
                font-weight: 500;
            }

            .link:hover {
                text-decoration: underline;
            }

            .footer {
                text-center: center;
                margin-top: 24px;
                font-size: 12px;
                color: #706f6c;
            }

            .footer a {
                color: #706f6c;
                text-decoration: underline;
            }

            .footer a:hover {
                color: #f53003;
            }
        </style>
    @endif
</head>
<body>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Créer un compte</h1>
            <p>Rejoignez TaskFlow pour gérer vos tâches efficacement</p>
        </div>

        <!-- Formulaire d'inscription -->
        <div class="form-container">

            @if (session('success'))
                <div class="alert alert-success">
                    <p>{{ session('success') }}</p>
                </div>
            @endif

            @if ($errors->any())
                <div class="alert alert-error">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('inscrire') }}">
                @csrf


                <!-- Nom complet -->
                <div class="form-group">
                    <label for="name" class="form-label">
                        Nom complet
                    </label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        value="{{ old('name') }}"
                        required
                        autofocus
                        class="form-input"
                        placeholder="Entrez votre nom complet"
                    >
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">
                        Adresse email
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value="{{ old('email') }}"
                        required
                        class="form-input"
                        placeholder="<EMAIL>"
                    >
                </div>

                <!-- Mot de passe -->
                <div class="form-group">
                    <label for="password" class="form-label">
                        Mot de passe
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        class="form-input"
                        placeholder="Créez un mot de passe sécurisé"
                    >
                </div>

                <!-- Confirmation mot de passe -->
                <div class="form-group">
                    <label for="password_confirmation" class="form-label">
                        Confirmer le mot de passe
                    </label>
                    <input
                        type="password"
                        id="password_confirmation"
                        name="password_confirmation"
                        required
                        class="form-input"
                        placeholder="Confirmez votre mot de passe"
                    >
                </div>

                <!-- Bouton d'inscription -->
                <button type="submit" class="btn">
                    Créer mon compte
                </button>

                {{-- <!-- Lien vers connexion -->
                <div class="text-center" style="padding-top: 16px;">
                    <p style="font-size: 14px; color: #706f6c;">
                        Vous avez déjà un compte ?
                        <a href="{{ route('login') }}" class="link">
                            Se connecter
                        </a>
                    </p>
                </div> --}}
            </form>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                En créant un compte, vous acceptez nos
                <a href="#">conditions d'utilisation</a>
                et notre
                <a href="#">politique de confidentialité</a>
            </p>
        </div>
    </div>

</body>
</html>