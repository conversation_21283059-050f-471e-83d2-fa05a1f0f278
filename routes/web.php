<?php

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Routes d'inscription
Route::get('/inscrire', [UserController::class, 'create'])->name('register');
Route::post('/inscrire', [UserController::class, 'store'])->name('inscrire');
Route::get('/login', [UserController::class, 'login'])->name('login');
// // Route de connexion (temporaire)
// Route::get('/login', function () {
//     return view('auth.login'); 
// })->name('login');