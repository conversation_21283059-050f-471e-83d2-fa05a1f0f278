<?php

use App\Http\Controllers\TeamController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});


Route::get('/inscrire', [UserController::class, 'create'])->name('register');
Route::post('/inscrire', [UserController::class, 'store'])->name('inscrire');


Route::get('/login', [UserController::class, 'showLoginForm'])->name('login');
Route::post('/login', [UserController::class, 'login'])->name('login.submit');

Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');


Route::post('/logout', [UserController::class, 'logout'])->name('logout');

// Routes pour les équipes
Route::post('/teams', [TeamController::class, 'store'])->name('teams.store');