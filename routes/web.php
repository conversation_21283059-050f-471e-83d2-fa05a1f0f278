<?php

use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Routes d'inscription
Route::get('/inscrire', [UserController::class, 'create'])->name('register');
Route::post('/inscrire', [UserController::class, 'store'])->name('inscrire');

// Routes de connexion
Route::get('/login', [UserController::class, 'showLoginForm'])->name('login');
Route::post('/login', [UserController::class, 'login'])->name('login.submit');

// // Route dashboard temporaire
// Route::get('/dashboard', function () {
//     return 'Bienvenue sur le dashboard !';
// })->name('dashboard');