<?php

use App\Http\Controllers\Api\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques (sans authentification)
Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

// Routes protégées par JWT
Route::middleware('jwt.auth')->group(function () {
    
    // Routes d'authentification
    Route::prefix('auth')->group(function () {
        Route::get('/me', [AuthController::class, 'me']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/verify', [AuthController::class, 'verify']);
    });
    
    // Routes utilisateur protégées
    Route::get('/user', function (Request $request) {
        return response()->json([
            'success' => true,
            'data' => $request->auth_user
        ]);
    });
    
    // Routes équipes protégées (à ajouter plus tard)
    Route::prefix('teams')->group(function () {
        // Route::get('/', [TeamController::class, 'index']);
        // Route::post('/', [TeamController::class, 'store']);
        // Route::get('/{id}', [TeamController::class, 'show']);
        // Route::put('/{id}', [TeamController::class, 'update']);
        // Route::delete('/{id}', [TeamController::class, 'destroy']);
    });
});

// Route de test pour vérifier que l'API fonctionne
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API TaskFlow fonctionne correctement !',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
