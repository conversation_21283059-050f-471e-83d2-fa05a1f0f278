<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::create('team_user', function (Blueprint $table) {
    $table->id();

    // Clés étrangères
    $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
    $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');

    // Rôle de l'utilisateur dans l'équipe
    $table->enum('role', ['leader', 'member'])->default('member');

    // Statut de l'invitation/participation
    $table->enum('status', ['pending', 'active', 'inactive'])->default('active');

    // Date de jonction
    $table->timestamp('joined_at')->useCurrent();

    // Timestamps
    $table->timestamps();

    // Index pour les performances
    $table->index(['user_id', 'team_id']);
    $table->index(['team_id', 'role']);

    // Contrainte unique : un utilisateur ne peut avoir qu'un seul rôle par équipe
    $table->unique(['user_id', 'team_id'], 'unique_user_team');
});

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_user');
    }
};
