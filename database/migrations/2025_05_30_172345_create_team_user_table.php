<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::create('team_user', function (Blueprint $table) {
    $table->id();

    // Clés étrangères
    $table->unsignedBigInteger('user_id');
    $table->unsignedBigInteger('team_id');

    $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
    $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade');

    // Rôle de l'utilisateur dans l'équipe
    $table->enum('role', ['leader', 'user'])->default('user');

    // Date de jonction
    $table->timestamp('join_date')->useCurrent();

  
    
    $table->timestamps();
});

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_user');
    }
};
