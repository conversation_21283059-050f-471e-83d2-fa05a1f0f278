<?php

namespace App\Http\Middleware;

use Closure;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\User;

class authMiddlware
{
    /**
     * Handle an incoming request - Middleware principal
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier le token JWT dans les requêtes API
        $token =$request->bearerToken();

        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Token d\'authentification requis'
            ], 401);
        }

        try {
            $decoded = $this->verifyToken($token);

            // Ajouter les informations utilisateur à la requête
            $user = User::find($decoded->id);
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Utilisateur non trouvé'
                ], 401);
            }

            $request->attributes->add([
                'user' => $user,
              
            ]);

        } catch (ExpiredException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token expiré'
            ], 401);
        } catch (SignatureInvalidException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Token invalide'
            ], 401);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur d\'authentification'
            ], 401);
        }

        return $next($request);
    }

    /**
     * Générer un token JWT pour un utilisateur
     */
    public static function generateToken(User $user, $remember = false)
    {
        $key = env('JWT_SECRET', env('APP_KEY'));

        // Durée d'expiration : 1 heure par défaut, 30 jours si "remember me"
        $expiration = $remember ? time() + (30 * 24 * 60 * 60) : time() + 3600;

        $payload = [
            'iat' => time(), // Date d'émission
            'exp' => $expiration, // Expiration
            'id' => $user->id, // ID de l'utilisateur
            'email' => $user->email,
            'name' => $user->name,
            'role' => $user->role ?? 'user',
        ];

        $jwt = JWT::encode($payload, $key, 'HS256');

        return [
            'token' => $jwt,
            'token_type' => 'Bearer',
            'expires_in' => $expiration,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role ?? 'user'
            ]
        ];
    }

    /**
     * Vérifier et décoder un token JWT
     */
    public function verifyToken($token)
    {
        $key = env('JWT_SECRET', env('APP_KEY'));
        return JWT::decode($token, new Key($key, 'HS256'));
    }




}
