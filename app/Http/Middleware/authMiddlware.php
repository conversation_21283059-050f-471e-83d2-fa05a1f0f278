<?php

namespace App\Http\Middleware;

use Closure;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class authMiddlware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        return $next($request);
    }
     public function generateToken()
    {
        $key = env('JWT_SECRET'); // Clé secrète (stockée dans .env)
        $payload = [
            'iss' => 'your-application-name', // Émetteur
            'aud' => 'your-audience', // Audience
            'iat' => time(), // Date d'émission
            'exp' => time() + 3600, // Expiration dans 1 heure
            'sub' => 1, // ID de l'utilisateur
            'name' => '<PERSON>', // Données supplémentaires
        ];

        $jwt = JWT::encode($payload, $key, 'HS256');
        return response()->json(['token' => $jwt]);
    }
    public function verifyToken(Request $request)
{
    $token = $request->bearerToken(); // Récupérer le token depuis l'en-tête Authorization

    if (!$token) {
        return response()->json(['error' => 'Token not provided'], 401);
    }

    try {
        $key = env('JWT_SECRET');
        $decoded = JWT::decode($token, new Key($key, 'HS256'));
        return response()->json(['user' => $decoded]);
    } catch (\Exception $e) {
        return response()->json(['error' => 'Invalid token'], 401);
    }
}
}
