<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Team;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        return view('addUsers.inscription');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validateData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6|confirmed',
        ]);

        $user = new User();
        $user->name = $validateData['name'];
        $user->email = $validateData['email'];
        $user->password = Hash::make($validateData['password']);
        $user->save();

        return redirect()->route('register')->with('success', 'Compte créé avec succès !');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
        $data=User::find($id);
        $data->delete();
        return redirect()->back()->with('success', 'Utilisateur supprimé avec succès !');
    }
    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        return view('login.login');
    }

    /**
     * Handle login request.
     */
    public function login(Request $request)
    {
        $validateData = $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);

        $user = User::where('email', $validateData['email'])->first();

        if ($user) {
            if (Hash::check($validateData['password'], $user->password)) {
                  Auth::login($user);
               
                return redirect()->route('dashboard')->with('success', 'Connexion réussie !');
            } else {
                return redirect()->route('login')
                    ->withInput($request->only('email'))
                    ->with('error', 'Mot de passe incorrect. Veuillez réessayer.');
            }
        } else {
            return redirect()->route('login')
                ->withInput($request->only('email'))
                ->with('error', 'Aucun compte trouvé avec cette adresse email.');
        }
    }
    /**
     * Show the dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();
        $teams = Team::where('created_by', $user->id)->with('members')->latest()->take(6)->get();

        // Statistiques pour le dashboard
        $stats = [
            'teams_count' => Team::where('created_by', $user->id)->count(),
            'total_members' => $teams->sum(function($team) { return $team->members->count(); }),
            'projects_count' => 12, // À remplacer par le vrai count quand vous aurez des projets
            'success_rate' => 94 // À calculer selon votre logique
        ];

        return view('dashbord.dashbord', compact('user', 'teams', 'stats'));
    }

    /**
     * Store a new team.
     */
    public function storeTeam(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('teams', 'public');
        }

        $team = Team::create([
            'name' => $validatedData['name'],
            'description' => $validatedData['description'],
            'image' => $imagePath,
            'created_by' => Auth::id(),
        ]);

        // Ajouter le créateur comme membre de l'équipe
        $team->members()->attach(Auth::id());

        // Changer le rôle de l'utilisateur à "leader"
        User::where('id', Auth::id())->update(['role' => 'leader']);

        return redirect()->route('dashboard')->with('success', 'Équipe créée avec succès ! Vous êtes maintenant le leader de cette équipe.');
    }

    /**
     * Handle logout request.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('login')->with('success', 'Vous avez été déconnecté avec succès.');
    }
}
