<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Middleware\authMiddlware;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * Inscription via API avec JWT
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'user'
            ]);

            $tokenData = authMiddlware::generateToken($user, $request->boolean('remember'));

            return response()->json([
                'success' => true,
                'message' => 'Inscription réussie',
                'data' => $tokenData
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'inscription'
            ], 500);
        }
    }

    /**
     * Connexion via API avec JWT
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Email ou mot de passe incorrect'
            ], 401);
        }

        try {
            $tokenData = authMiddlware::generateToken($user, $request->boolean('remember'));

            return response()->json([
                'success' => true,
                'message' => 'Connexion réussie',
                'data' => $tokenData
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la génération du token'
            ], 500);
        }
    }

    /**
     * Obtenir les informations de l'utilisateur connecté
     */
    public function me(Request $request)
    {
        $user = $request->auth_user;
        $payload = $request->jwt_payload;

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role ?? 'user',
                    'created_at' => $user->created_at,
                ],
                'token_info' => [
                    'issued_at' => date('Y-m-d H:i:s', $payload->iat),
                    'expires_at' => date('Y-m-d H:i:s', $payload->exp),
                ]
            ]
        ], 200);
    }


    /**
     * Déconnexion (côté client, le token sera supprimé)
     */
    public function logout(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Déconnexion réussie. Supprimez le token côté client.'
        ], 200);
    }

    /**
     * Vérifier la validité d'un token
     */
    public function verify(Request $request)
    {
        $user = $request->auth_user;
        $payload = $request->jwt_payload;

        return response()->json([
            'success' => true,
            'message' => 'Token valide',
            'data' => [
                'valid' => true,
                'user_id' => $user->id,
                'expires_at' => date('Y-m-d H:i:s', $payload->exp),
                'time_remaining' => $payload->exp - time() . ' secondes'
            ]
        ], 200);
    }
}
