<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get teams where user is leader.
     */
    public function leaderTeams()
    {
        return $this->hasMany(Team::class, 'created_by');
    }

    /**
     * Get all teams where user is member (any role).
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_user')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get teams where user is leader.
     */
    public function leaderTeamsViaRole()
    {
        return $this->belongsToMany(Team::class, 'team_user')
                    ->wherePivot('role', 'leader')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get teams where user is regular member.
     */
    public function memberTeams()
    {
        return $this->belongsToMany(Team::class, 'team_user')
                    ->wherePivot('role', 'member')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get active teams only.
     */
    public function activeTeams()
    {
        return $this->belongsToMany(Team::class, 'team_user')
                    ->wherePivot('status', 'active')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Check if user is leader of a specific team.
     */
    public function isLeaderOf($teamId)
    {
        return $this->teams()
                    ->wherePivot('team_id', $teamId)
                    ->wherePivot('role', 'leader')
                    ->exists();
    }

    /**
     * Check if user is member of a specific team.
     */
    public function isMemberOf($teamId)
    {
        return $this->teams()
                    ->wherePivot('team_id', $teamId)
                    ->exists();
    }

    /**
     * Get user's role in a specific team.
     */
    public function getRoleInTeam($teamId)
    {
        $team = $this->teams()
                     ->wherePivot('team_id', $teamId)
                     ->first();

        return $team ? $team->pivot->role : null;
    }

    /**
     * Join a team with a specific role.
     */
    public function joinTeam($teamId, $role = 'member', $status = 'active')
    {
        return $this->teams()->attach($teamId, [
            'role' => $role,
            'status' => $status,
            'joined_at' => now()
        ]);
    }

    /**
     * Leave a team.
     */
    public function leaveTeam($teamId)
    {
        return $this->teams()->detach($teamId);
    }

    /**
     * Get count of teams where user is leader.
     */
    public function getLeaderTeamsCountAttribute()
    {
        return $this->leaderTeamsViaRole()->count();
    }

    /**
     * Get count of all teams user belongs to.
     */
    public function getAllTeamsCountAttribute()
    {
        return $this->activeTeams()->count();
    }
}
