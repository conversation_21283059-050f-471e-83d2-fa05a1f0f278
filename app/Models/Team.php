<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'image',
        'created_by',
    ];

    /**
     * Get the creator/leader of the team.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * <PERSON>as for creator (leader).
     */
    public function leader()
    {
        return $this->creator();
    }

    /**
     * Get all members of the team with their roles.
     */
    public function members()
    {
        return $this->belongsToMany(User::class, 'team_user')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get only leaders of the team.
     */
    public function leaders()
    {
        return $this->belongsToMany(User::class, 'team_user')
                    ->wherePivot('role', 'leader')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get only regular members (non-leaders).
     */
    public function regularMembers()
    {
        return $this->belongsToMany(User::class, 'team_user')
                    ->wherePivot('role', 'member')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get active members only.
     */
    public function activeMembers()
    {
        return $this->belongsToMany(User::class, 'team_user')
                    ->wherePivot('status', 'active')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Check if user is a leader of this team.
     */
    public function isLeader($userId)
    {
        return $this->members()
                    ->wherePivot('user_id', $userId)
                    ->wherePivot('role', 'leader')
                    ->exists();
    }

    /**
     * Check if user is a member of this team (any role).
     */
    public function isMember($userId)
    {
        return $this->members()
                    ->wherePivot('user_id', $userId)
                    ->exists();
    }

    /**
     * Get user's role in this team.
     */
    public function getUserRole($userId)
    {
        $member = $this->members()
                       ->wherePivot('user_id', $userId)
                       ->first();

        return $member ? $member->pivot->role : null;
    }

    /**
     * Add a user to the team with a specific role.
     */
    public function addMember($userId, $role = 'member', $status = 'active')
    {
        return $this->members()->attach($userId, [
            'role' => $role,
            'status' => $status,
            'joined_at' => now()
        ]);
    }

    /**
     * Update user's role in the team.
     */
    public function updateMemberRole($userId, $role)
    {
        return $this->members()->updateExistingPivot($userId, [
            'role' => $role
        ]);
    }

    /**
     * Remove a user from the team.
     */
    public function removeMember($userId)
    {
        return $this->members()->detach($userId);
    }

    /**
     * Get team member count.
     */
    public function getMemberCountAttribute()
    {
        return $this->activeMembers()->count();
    }

    /**
     * Get team leader count.
     */
    public function getLeaderCountAttribute()
    {
        return $this->leaders()->count();
    }
}
