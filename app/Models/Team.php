<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'image',
        'created_by',
    ];

    /**
     * Get the creator/leader of the team.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * <PERSON><PERSON> for creator (leader).
     */
    public function leader()
    {
        return $this->creator();
    }

    /**
     * Get all members of the team.
     */
    public function members()
    {
        return $this->belongsToMany(User::class, 'team_user')->withTimestamps();
    }

    /**
     * Check if user is the leader of this team.
     */
    public function isLeader($userId)
    {
        return $this->created_by == $userId;
    }

    /**
     * Get team with member count.
     */
    public function getMemberCountAttribute()
    {
        return $this->members()->count();
    }
}
